#!/usr/bin/env python3
"""Debug Environment Variables"""

import os
from masonite.environment import env

def debug_environment_variables():
    """Debug environment variables to see what's being read"""
    print("🔍 Debugging Environment Variables")
    print("=" * 50)
    
    # Check .env file exists
    env_file_path = ".env"
    if os.path.exists(env_file_path):
        print(f"✅ .env file exists at: {os.path.abspath(env_file_path)}")
    else:
        print(f"❌ .env file not found at: {os.path.abspath(env_file_path)}")
    
    # Read .env file content
    try:
        with open(env_file_path, 'r') as f:
            content = f.read()
            print(f"\n📄 .env file content (first 500 chars):")
            print(content[:500])
            
            # Look for CORS_ORIGIN specifically
            lines = content.split('\n')
            cors_lines = [line for line in lines if 'CORS_ORIGIN' in line]
            print(f"\n🔍 CORS_ORIGIN lines in .env:")
            for line in cors_lines:
                print(f"  {line}")
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
    
    # Check environment variables
    print(f"\n🔍 Environment Variable Checks:")
    print(f"os.environ.get('CORS_ORIGIN'): {os.environ.get('CORS_ORIGIN', 'NOT_SET')}")
    print(f"env('CORS_ORIGIN'): {env('CORS_ORIGIN', 'NOT_SET')}")
    print(f"env('CORS_ORIGIN', 'http://localhost:4200'): {env('CORS_ORIGIN', 'http://localhost:4200')}")
    
    # Check other related variables
    print(f"\n🔍 Related Environment Variables:")
    print(f"FRONTEND_URL: {env('FRONTEND_URL', 'NOT_SET')}")
    print(f"APP_URL: {env('APP_URL', 'NOT_SET')}")
    print(f"APP_PORT: {env('APP_PORT', 'NOT_SET')}")
    
    # Test the exact code from OAuthController
    print(f"\n🔍 Testing OAuthController logic:")
    frontend_url = env('CORS_ORIGIN', 'http://localhost:4200')
    print(f"frontend_url = env('CORS_ORIGIN', 'http://localhost:4200') = {frontend_url}")
    
    # Test redirect URL construction
    error = "access_denied"
    error_url = f"{frontend_url}/auth/oauth-error?error={error}"
    print(f"error_url = f\"{frontend_url}/auth/oauth-error?error={error}\" = {error_url}")

if __name__ == "__main__":
    debug_environment_variables()
