#!/usr/bin/env python3
"""Test OAuth Redirect Fix"""

import requests
import time

def test_oauth_redirect_fix():
    """Test if OAuth redirect is now using correct frontend URL"""
    base_url = "http://localhost:3002"
    
    print("🧪 Testing OAuth Redirect Fix")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            time.sleep(1)
    else:
        print("❌ Server not responding")
        return
    
    # Test OAuth redirect with error
    print("\n🔄 Testing OAuth redirect with error...")
    try:
        response = requests.get(
            f"{base_url}/api/auth/oauth/callback",
            params={
                'error': 'access_denied',
                'state': 'test_state'
            },
            allow_redirects=False,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', 'No Location header')
            print(f"🔗 Redirect URL: {redirect_url}")
            
            # Check if redirect URL contains correct frontend URL
            if 'http://localhost:4200/auth/oauth-error' in redirect_url:
                print("✅ SUCCESS: Redirect URL now contains correct frontend URL (http://localhost:4200)")
                print("✅ OAuth redirect fix is working!")
                return True
            elif 'http://localhost/auth/oauth-error' in redirect_url:
                print("❌ STILL BROKEN: Redirect URL still missing port 4200")
                print("❌ OAuth redirect fix did not work")
                return False
            else:
                print(f"❌ UNEXPECTED: Redirect URL format unexpected: {redirect_url}")
                return False
        else:
            print(f"❌ Expected 302 redirect, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OAuth redirect: {e}")
        return False

if __name__ == "__main__":
    success = test_oauth_redirect_fix()
    if success:
        print("\n🎉 OAuth redirect fix is working correctly!")
    else:
        print("\n❌ OAuth redirect fix needs more work")
