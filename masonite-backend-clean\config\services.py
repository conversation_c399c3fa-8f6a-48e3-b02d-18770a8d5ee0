"""Services Configuration"""

from masonite.environment import env

"""
|--------------------------------------------------------------------------
| Third Party Services Configuration
|--------------------------------------------------------------------------
|
| This file is for storing the credentials for third party services such
| as payment gateways, email services, SMS services, etc.
|
"""

SERVICES = {
    # Razorpay Payment Gateway Configuration
    'razorpay': {
        'key_id': env('RAZORPAY_KEY_ID', 'rzp_test_V1lTfJTbc1xDV7'),
        'key_secret': env('RAZORPAY_KEY_SECRET', 'your_secret_key_here'),
        'webhook_secret': env('RAZORPAY_WEBHOOK_SECRET', 'your_webhook_secret_here'),
        'currency': env('RAZORPAY_CURRENCY', 'INR'),
        'environment': env('RAZORPAY_ENVIRONMENT', 'test'),  # test or live
    },
    
    # Stripe Payment Gateway Configuration (for future use)
    'stripe': {
        'public_key': env('STRIPE_PUBLIC_KEY', ''),
        'secret_key': env('STRIPE_SECRET_KEY', ''),
        'webhook_secret': env('STRIPE_WEBHOOK_SECRET', ''),
        'currency': env('STRIPE_CURRENCY', 'usd'),
    },
    
    # Twilio SMS Service Configuration
    'twilio': {
        'account_sid': env('TWILIO_ACCOUNT_SID', ''),
        'auth_token': env('TWILIO_AUTH_TOKEN', ''),
        'from_number': env('TWILIO_FROM_NUMBER', ''),
    },
    
    # OAuth Provider Configurations
    'oauth': {
        'google': {
            'client_id': env('GOOGLE_CLIENT_ID', ''),
            'client_secret': env('GOOGLE_CLIENT_SECRET', ''),
            'redirect_uri': env('GOOGLE_REDIRECT_URI', 'http://localhost:3002/api/oauth/callback'),
        },
        'github': {
            'client_id': env('GITHUB_CLIENT_ID', ''),
            'client_secret': env('GITHUB_CLIENT_SECRET', ''),
            'redirect_uri': env('GITHUB_REDIRECT_URI', 'http://localhost:3002/api/oauth/callback'),
        },
        'microsoft': {
            'client_id': env('MICROSOFT_CLIENT_ID', ''),
            'client_secret': env('MICROSOFT_CLIENT_SECRET', ''),
            'redirect_uri': env('MICROSOFT_REDIRECT_URI', 'http://localhost:3002/api/oauth/callback'),
        },
    },
    
    # Email Service Configuration (additional to mail.py)
    'email': {
        'sendgrid': {
            'api_key': env('SENDGRID_API_KEY', ''),
        },
        'mailgun': {
            'domain': env('MAILGUN_DOMAIN', ''),
            'secret': env('MAILGUN_SECRET', ''),
        },
    },
    
    # File Storage Services
    'storage': {
        'aws': {
            'access_key_id': env('AWS_ACCESS_KEY_ID', ''),
            'secret_access_key': env('AWS_SECRET_ACCESS_KEY', ''),
            'default_region': env('AWS_DEFAULT_REGION', 'us-east-1'),
            'bucket': env('AWS_BUCKET', ''),
        },
        'cloudinary': {
            'cloud_name': env('CLOUDINARY_CLOUD_NAME', ''),
            'api_key': env('CLOUDINARY_API_KEY', ''),
            'api_secret': env('CLOUDINARY_API_SECRET', ''),
        },
    },
    
    # Analytics and Monitoring
    'analytics': {
        'google_analytics': {
            'tracking_id': env('GOOGLE_ANALYTICS_TRACKING_ID', ''),
        },
        'mixpanel': {
            'token': env('MIXPANEL_TOKEN', ''),
        },
    },
    
    # Push Notification Services
    'push': {
        'firebase': {
            'server_key': env('FIREBASE_SERVER_KEY', ''),
            'sender_id': env('FIREBASE_SENDER_ID', ''),
        },
        'pusher': {
            'app_id': env('PUSHER_APP_ID', ''),
            'key': env('PUSHER_APP_KEY', ''),
            'secret': env('PUSHER_APP_SECRET', ''),
            'cluster': env('PUSHER_APP_CLUSTER', 'mt1'),
        },
    },
}
