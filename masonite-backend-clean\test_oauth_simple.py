#!/usr/bin/env python3
"""Simple OAuth Test"""

import requests
import time

def test_oauth_simple():
    """Simple OAuth test"""
    base_url = "http://localhost:3002"
    
    print("🔄 Making OAuth callback request...")
    
    try:
        response = requests.get(
            f"{base_url}/api/auth/oauth/callback",
            params={
                'error': 'access_denied',
                'state': 'test_state_simple'
            },
            allow_redirects=False,
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Location: {response.headers.get('Location', 'No Location')}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_oauth_simple()
