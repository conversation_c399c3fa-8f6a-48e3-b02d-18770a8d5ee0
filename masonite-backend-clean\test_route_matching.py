#!/usr/bin/env python3
"""Test Route Matching"""

import requests
import time

def test_route_matching():
    """Test which route is being matched for OAuth callback"""
    base_url = "http://localhost:3002"
    
    print("🔍 Testing Route Matching for OAuth Callback")
    print("=" * 50)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            time.sleep(1)
    else:
        print("❌ Server not responding")
        return
    
    # Test different OAuth callback URLs to see which one works
    test_urls = [
        "/api/auth/oauth/callback",
        "/auth/oauth/callback", 
        "/api/oauth/callback",
        "/oauth/callback"
    ]
    
    for url in test_urls:
        print(f"\n🔄 Testing URL: {url}")
        try:
            response = requests.get(
                f"{base_url}{url}",
                params={
                    'error': 'test_error',
                    'state': 'test_state'
                },
                allow_redirects=False,
                timeout=10
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 302:
                redirect_url = response.headers.get('Location', 'No Location')
                print(f"   Redirect: {redirect_url}")
                
                if "http://localhost:4200" in redirect_url:
                    print("   ✅ CORRECT: Uses frontend URL with port 4200")
                elif "http://localhost/" in redirect_url:
                    print("   ❌ WRONG: Missing port in frontend URL")
                else:
                    print(f"   ❓ UNKNOWN: Unexpected redirect format")
            elif response.status_code == 404:
                print("   ❌ Route not found")
            elif response.status_code == 405:
                print("   ❌ Method not allowed")
            else:
                print(f"   ❓ Unexpected status: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Test the test endpoint to confirm OAuth controller is working
    print(f"\n🔄 Testing OAuth test endpoint...")
    try:
        response = requests.get(f"{base_url}/api/auth/oauth/test", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Frontend URL: {data.get('frontend_url', 'Not found')}")
            print("   ✅ OAuth controller is accessible")
        else:
            print(f"   ❌ Test endpoint failed: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Test endpoint error: {e}")

if __name__ == "__main__":
    test_route_matching()
