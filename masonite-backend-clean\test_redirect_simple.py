#!/usr/bin/env python3
"""Test Simple Redirect"""

import requests
import time

def test_simple_redirect():
    """Test simple redirect to see if Masonite redirect is working correctly"""
    base_url = "http://localhost:3002"
    
    print("🔄 Testing simple redirect...")
    
    # Wait for server to be ready
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            time.sleep(1)
    else:
        print("❌ Server not responding")
        return
    
    try:
        # Test a simple endpoint that should redirect
        response = requests.get(
            f"{base_url}/api/auth/oauth/callback",
            params={
                'error': 'test_error',
                'state': 'test_state'
            },
            allow_redirects=False,
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        redirect_url = response.headers.get('Location', 'No Location')
        print(f"Redirect URL: {redirect_url}")
        
        # Check what we expect vs what we get
        expected_url = "http://localhost:4200/auth/oauth-error?error=test_error"
        print(f"Expected: {expected_url}")
        print(f"Actual:   {redirect_url}")
        
        if redirect_url == expected_url:
            print("✅ Redirect URL is correct!")
            return True
        else:
            print("❌ Redirect URL is incorrect!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_simple_redirect()
