#!/usr/bin/env python3
"""Test OAuth Controller Debug"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from masonite.environment import env
from app.controllers.OAuthController import OAuthController

def test_oauth_controller_initialization():
    """Test OAuth controller initialization to see what frontend_url is set to"""
    print("🔍 Testing OAuth Controller Initialization")
    print("=" * 50)
    
    # Test environment loading
    print(f"env('CORS_ORIGIN'): {env('CORS_ORIGIN', 'NOT_SET')}")
    print(f"env('CORS_ORIGIN', 'http://localhost:4200'): {env('CORS_ORIGIN', 'http://localhost:4200')}")
    
    # Create OAuth controller instance
    print("\n🔄 Creating OAuthController instance...")
    try:
        oauth_controller = OAuthController()
        print(f"✅ OAuthController created successfully")
        print(f"🔍 oauth_controller.frontend_url = {oauth_controller.frontend_url}")
        
        # Test redirect URL construction
        error = "access_denied"
        error_url = f"{oauth_controller.frontend_url}/auth/oauth-error?error={error}"
        print(f"🔍 Constructed error_url = {error_url}")
        
        if "http://localhost:4200" in error_url:
            print("✅ SUCCESS: OAuth controller is using correct frontend URL")
            return True
        else:
            print("❌ FAILURE: OAuth controller is not using correct frontend URL")
            return False
            
    except Exception as e:
        print(f"❌ Error creating OAuthController: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_oauth_controller_initialization()
    if success:
        print("\n🎉 OAuth controller initialization is working correctly!")
    else:
        print("\n❌ OAuth controller initialization has issues")
