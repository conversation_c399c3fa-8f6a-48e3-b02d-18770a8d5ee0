#!/usr/bin/env python3
"""Debug OAuth Redirect in Detail"""

import requests
import time

def debug_oauth_redirect_detailed():
    """Debug OAuth redirect in detail to see what's happening"""
    base_url = "http://localhost:3002"
    
    print("🔍 Debugging OAuth Redirect in Detail")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            time.sleep(1)
    else:
        print("❌ Server not responding")
        return
    
    # Test OAuth redirect with detailed logging
    print("\n🔄 Testing OAuth redirect with detailed logging...")
    try:
        url = f"{base_url}/api/auth/oauth/callback"
        params = {
            'error': 'access_denied',
            'state': 'test_state_debug'
        }
        
        print(f"🔗 Making request to: {url}")
        print(f"🔗 With parameters: {params}")
        
        response = requests.get(
            url,
            params=params,
            allow_redirects=False,
            timeout=10
        )
        
        print(f"\n📊 Response Details:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print(f"   Content: {response.text[:200]}...")
        
        if response.status_code == 302:
            redirect_url = response.headers.get('Location', 'No Location header')
            print(f"\n🔗 Redirect Analysis:")
            print(f"   Full Redirect URL: {redirect_url}")
            
            # Parse the redirect URL
            if redirect_url.startswith('http://localhost:4200'):
                print("   ✅ Correct frontend URL (http://localhost:4200)")
            elif redirect_url.startswith('http://localhost/'):
                print("   ❌ Missing port - redirecting to http://localhost/")
            elif redirect_url.startswith('http://localhost:3002'):
                print("   ❌ Wrong port - redirecting to backend port")
            else:
                print(f"   ❌ Unexpected URL format: {redirect_url}")
                
            # Check if it contains the error parameter
            if 'error=access_denied' in redirect_url:
                print("   ✅ Error parameter preserved")
            else:
                print("   ❌ Error parameter missing or modified")
                
            return redirect_url
        else:
            print(f"❌ Expected 302 redirect, got {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing OAuth redirect: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_direct_controller_access():
    """Test direct controller access to see if the issue is in routing"""
    print("\n🔍 Testing Direct Controller Access")
    print("=" * 40)
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.controllers.OAuthController import OAuthController
        from masonite.request import Request
        from masonite.response import Response
        
        # Create mock request and response
        oauth_controller = OAuthController()
        print(f"✅ OAuthController created")
        print(f"🔍 frontend_url = {oauth_controller.frontend_url}")
        
        # Test URL construction
        error = "access_denied"
        error_url = f"{oauth_controller.frontend_url}/auth/oauth-error?error={error}"
        print(f"🔍 Constructed error_url = {error_url}")
        
        return error_url
        
    except Exception as e:
        print(f"❌ Error testing direct controller access: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Test via HTTP request
    http_redirect_url = debug_oauth_redirect_detailed()
    
    # Test direct controller access
    direct_error_url = test_direct_controller_access()
    
    print("\n📊 Comparison:")
    print(f"   HTTP Redirect URL: {http_redirect_url}")
    print(f"   Direct Controller URL: {direct_error_url}")
    
    if http_redirect_url and direct_error_url:
        if http_redirect_url == direct_error_url:
            print("   ✅ URLs match - issue is in controller logic")
        else:
            print("   ❌ URLs differ - issue might be in routing or middleware")
    
    print("\n🎯 Conclusion:")
    if http_redirect_url and "http://localhost:4200" in http_redirect_url:
        print("   ✅ OAuth redirect is working correctly!")
    else:
        print("   ❌ OAuth redirect needs fixing")
